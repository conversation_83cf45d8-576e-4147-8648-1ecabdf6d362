{"mcpEndpoint": ["-"], "mcpServers": {"browsermcp": {"command": "npx", "args": ["-y", "@browsermcp/mcp@latest"]}}, "mcpServerConfig": {"browsermcp": {"tools": {"browser_navigate": {"description": "Navigate to a URL", "enable": true}, "browser_go_back": {"description": "Go back to the previous page", "enable": true}, "browser_go_forward": {"description": "Go forward to the next page", "enable": true}, "browser_snapshot": {"description": "Capture accessibility snapshot of the current page. Use this for getting references to elements to interact with.", "enable": true}, "browser_click": {"description": "Perform click on a web page", "enable": true}, "browser_hover": {"description": "Hover over element on page", "enable": true}, "browser_type": {"description": "Type text into editable element", "enable": true}, "browser_select_option": {"description": "Select an option in a dropdown", "enable": true}, "browser_press_key": {"description": "Press a key on the keyboard", "enable": true}, "browser_wait": {"description": "Wait for a specified time in seconds", "enable": true}, "browser_get_console_logs": {"description": "Get the console logs from the browser", "enable": true}, "browser_screenshot": {"description": "Take a screenshot of the current page", "enable": true}}}}}