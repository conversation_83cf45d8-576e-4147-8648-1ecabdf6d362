# MCP服务远程映射项目

## 项目简介

本项目利用 [xiaozhi-client](https://github.com/shenjingnan/xiaozhi-client) 将本地MCP服务映射为远程服务，通过SSE（Server-Sent Events）协议提供远程访问能力。

## 技术选型

- **MCP服务**: @browsermcp/mcp - 浏览器自动化MCP服务
- **远程映射**: xiaozhi-client - 提供本地到远程的服务映射
- **通信协议**: SSE (Server-Sent Events)

## 快速开始

### 1. 测试本地MCP服务
```bash
npx @browsermcp/mcp@latest
```

### 2. 启动远程映射服务
```bash
# 使用默认端口 3000
xiaozhi start --server

# 使用自定义端口
xiaozhi start --server 8080
```

## 主要目录结构

```
.
├── README.md              # 项目说明文档
├── xiaozhi.config.json    # xiaozhi配置文件
└── docs/                  # 文档目录（待创建）
```

## 配置说明

项目配置文件 `xiaozhi.config.json` 包含：
- `mcpEndpoint`: 远程MCP端点配置
- `mcpServers`: 本地MCP服务配置，当前配置了browsermcp服务

## 项目进度规划

- [x] 创建项目README
- [ ] 测试本地MCP服务运行状态
- [ ] 启动xiaozhi远程映射服务
- [ ] 验证远程调用功能
- [ ] 完善项目文档

## 注意事项

1. 确保本地MCP服务能正常运行后再启动远程映射
2. 远程端点配置需要有效的WebSocket连接
3. 端口配置需要确保不与其他服务冲突

## 参考链接

- [xiaozhi-client项目](https://github.com/shenjingnan/xiaozhi-client)
- [browsermcp文档](https://www.npmjs.com/package/@browsermcp/mcp)
